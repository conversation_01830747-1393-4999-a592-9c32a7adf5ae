/**
 * 产品状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getProductList, getCategoryList } from '@/api/product'
import type { Product, Category, ProductListQuery, PageData } from '@/types'

export const useProductStore = defineStore('product', () => {
  // 状态
  const productList = ref<Product[]>([])
  const categoryList = ref<Category[]>([])
  const currentProduct = ref<Product | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    page_size: 10,
    total: 0,
    total_pages: 0
  })

  // 计算属性
  const hasProducts = computed(() => productList.value.length > 0)
  const hasCategories = computed(() => categoryList.value.length > 0)

  // 获取产品列表
  async function fetchProductList(params: ProductListQuery = { page: 1, page_size: 10 }) {
    try {
      loading.value = true
      const response = await getProductList(params)
      const data: PageData<Product> = response.data

      productList.value = data.list
      pagination.value = {
        page: data.page,
        page_size: data.page_size,
        total: data.total,
        total_pages: data.total_pages
      }

      return Promise.resolve(data)
    } catch (error) {
      ElMessage.error('获取产品列表失败')
      return Promise.reject(error)
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  async function fetchCategoryList() {
    try {
      const response = await getCategoryList()
      categoryList.value = response.data
      return Promise.resolve(response.data)
    } catch (error) {
      ElMessage.error('获取分类列表失败')
      return Promise.reject(error)
    }
  }

  // 设置当前产品
  function setCurrentProduct(product: Product | null) {
    currentProduct.value = product
  }

  // 添加产品到列表
  function addProduct(product: Product) {
    productList.value.unshift(product)
    pagination.value.total += 1
  }

  // 更新产品
  function updateProduct(updatedProduct: Product) {
    const index = productList.value.findIndex(p => p.id === updatedProduct.id)
    if (index !== -1) {
      productList.value[index] = updatedProduct
    }
    
    // 如果是当前产品，也要更新
    if (currentProduct.value && currentProduct.value.id === updatedProduct.id) {
      currentProduct.value = updatedProduct
    }
  }

  // 删除产品
  function removeProduct(productId: number) {
    const index = productList.value.findIndex(p => p.id === productId)
    if (index !== -1) {
      productList.value.splice(index, 1)
      pagination.value.total -= 1
    }
    
    // 如果删除的是当前产品，清空当前产品
    if (currentProduct.value && currentProduct.value.id === productId) {
      currentProduct.value = null
    }
  }

  // 根据分类筛选产品
  function getProductsByCategory(categoryId: number): Product[] {
    return productList.value.filter(product => product.category_id === categoryId)
  }

  // 根据状态筛选产品
  function getProductsByStatus(status: number): Product[] {
    return productList.value.filter(product => product.status === status)
  }

  // 搜索产品
  function searchProducts(keyword: string): Product[] {
    if (!keyword) return productList.value
    
    const lowerKeyword = keyword.toLowerCase()
    return productList.value.filter(product => 
      product.name.toLowerCase().includes(lowerKeyword) ||
      product.description.toLowerCase().includes(lowerKeyword)
    )
  }

  // 获取分类树
  function getCategoryTree(): Category[] {
    const categoryMap = new Map<number, Category>()
    const rootCategories: Category[] = []

    // 创建分类映射
    categoryList.value.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] })
    })

    // 构建树结构
    categoryMap.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(category)
      } else {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(category)
        }
      }
    })

    return rootCategories
  }

  // 根据ID获取分类
  function getCategoryById(id: number): Category | undefined {
    return categoryList.value.find(category => category.id === id)
  }

  // 重置产品状态
  function resetProductState() {
    productList.value = []
    currentProduct.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      page_size: 10,
      total: 0,
      total_pages: 0
    }
  }

  return {
    // 状态
    productList,
    categoryList,
    currentProduct,
    loading,
    pagination,
    
    // 计算属性
    hasProducts,
    hasCategories,
    
    // 方法
    fetchProductList,
    fetchCategoryList,
    setCurrentProduct,
    addProduct,
    updateProduct,
    removeProduct,
    getProductsByCategory,
    getProductsByStatus,
    searchProducts,
    getCategoryTree,
    getCategoryById,
    resetProductState
  }
})
