/**
 * 用户管理API
 */
import { get, post, put, del } from '@/utils/request'
import type { User } from './auth'

// 定义类型
export interface UserUpdateRequest {
  nickname?: string
  email?: string
  avatar?: string
}

export interface UserChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface UserListQuery {
  page?: number
  pageSize?: number
  keyword?: string
  role?: string
}

export interface PageData<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

/**
 * 获取用户列表
 */
export function getUserList(params: UserListQuery) {
  return get<PageData<User>>('/users', params)
}

/**
 * 获取用户详情
 */
export function getUserDetail(id: number) {
  return get<User>(`/users/${id}`)
}

/**
 * 更新用户信息
 */
export function updateUser(id: number, data: UserUpdateRequest) {
  return put<User>(`/users/${id}`, data)
}

/**
 * 删除用户
 */
export function deleteUser(id: number) {
  return del(`/users/${id}`)
}

/**
 * 更新个人资料
 */
export function updateProfile(data: UserUpdateRequest) {
  return put<User>('/users/profile', data)
}

/**
 * 修改密码
 */
export function changePassword(data: UserChangePasswordRequest) {
  return post('/users/change-password', data)
}
