<template>
  <div class="login-container">

    <div class="top-header">
      <div class="header-line left"></div>
      <div class="header-title">无人机巡检平台</div>
      <div class="header-line right"></div>
      <div class="header-info">
        <span></span>
        <span></span>
      </div>
    </div>

    <div class="login-form-container">
      <div class="login-form">
        <div class="form-title">登录</div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="form"
          size="large"
        >
          <el-form-item prop="username" class="form-item">
            <div class="input-group">
              <el-icon class="input-icon"><User /></el-icon>
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                class="custom-input"
                clearable
              />
            </div>
          </el-form-item>

          <el-form-item prop="password" class="form-item">
            <div class="input-group">
              <el-icon class="input-icon"><Lock /></el-icon>
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                class="custom-input"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </div>
          </el-form-item>

          <el-form-item class="form-item">
            <el-button
              type="primary"
              class="login-btn"
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)

const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

async function handleLogin() {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    if (loginForm.username === 'admin' && loginForm.password === '123456') {
      await new Promise(resolve => setTimeout(resolve, 1500))

      sessionStorage.setItem('isLoggedIn', 'true')
      sessionStorage.setItem('userInfo', JSON.stringify({
        username: 'admin',
        nickname: '系统管理员',
        role: 'admin'
      }))

      ElMessage.success('登录成功，欢迎使用系统！')

      const redirect = (route.query.redirect as string) || '/dashboard'
      router.push(redirect)
    } else {
      ElMessage.error('用户名或密码错误')
    }
  } catch (error) {
    console.error('Login error:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a1628 0%, #1a2332 50%, #0f1419 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}



.top-header {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  z-index: 10;
  width: 100%;
}

.header-line {
  position: absolute;
  top: 50%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
}

.header-line.left {
  left: 0;
  right: 50%;
  margin-right: 120px;
}

.header-line.right {
  left: 50%;
  right: 0;
  margin-left: 120px;
}

.header-line.left::before,
.header-line.right::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: #00d4ff;
  border-radius: 50%;
  top: -3px;
}

.header-line.left::before {
  right: 0;
}

.header-line.right::after {
  left: 0;
}

.header-title {
  color: #00d4ff;
  font-size: 28px;
  font-weight: 600;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  position: relative;
  z-index: 10;

  padding: 0 20px;
}

.header-info {
  position: absolute;
  top: -10px;
  right: -120px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  font-size: 12px;
  color: #7a8b9a;
}

.login-form-container {
  position: relative;
  z-index: 10;
  margin-top: 60px;
}

.login-form {
  width: 500px;
  padding: 50px;
  background: rgba(15, 20, 25, 0.8);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.login-form::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00d4ff, transparent, #00d4ff);
  border-radius: 8px;
  z-index: -1;
  opacity: 0.3;
}

.form-title {
  text-align: center;
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
}

/* 表单项居中 */
.form-item {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  margin-left: 65px;
}

.input-group {
  position: relative;
  width: 100%;
  max-width: 380px;
  display: flex;
  justify-content: center;
}

.input-icon {
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: #00d4ff;
  z-index: 10;
  font-size: 20px;
}

/* 输入框样式优化 - 内容居中 */
:deep(.custom-input) {
  width: 100%;
}

:deep(.custom-input .el-input__wrapper) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 6px;
  padding-left: 60px;
  padding-right: 20px;
  height: 60px;
  box-shadow: none;
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

:deep(.custom-input .el-input__inner) {
  color: #ffffff;
  font-size: 18px;
  background: transparent;
  height: 100%;
  text-align: left;
  padding: 0;
}

:deep(.custom-input .el-input__inner::placeholder) {
  color: #7a8b9a;
  text-align: left;
}

.remember-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

:deep(.remember-checkbox .el-checkbox__label) {
  color: #7a8b9a;
  font-size: 14px;
}

:deep(.remember-checkbox .el-checkbox__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 255, 0.3);
}

:deep(.remember-checkbox.is-checked .el-checkbox__inner) {
  background: #00d4ff;
  border-color: #00d4ff;
}

.captcha-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

:deep(.captcha-input) {
  width: 100px;
}

:deep(.captcha-input .el-input__wrapper) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 4px;
  height: 36px;
  padding: 0 10px;
}

:deep(.captcha-input .el-input__inner) {
  color: #ffffff;
  font-size: 14px;
}

.captcha-code {
  width: 60px;
  height: 36px;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4ff;
  font-weight: bold;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.captcha-code:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
}

/* 登录按钮样式 */
:deep(.login-btn) {
  width: 100%;
  max-width: 380px;
  height: 60px;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
  margin-top: 15px;
}

:deep(.login-btn:hover) {
  background: linear-gradient(135deg, #00b8e6, #0088bb);
  box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
  transform: translateY(-2px);
}

:deep(.login-btn:active) {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form {
    width: 90vw;
    max-width: 400px;
    padding: 30px 20px;
  }

  .header-title {
    font-size: 20px;
  }

  .header-line {
    width: 60px;
  }

  .header-info {
    right: -100px;
    font-size: 11px;
  }

  .form-title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .input-group {
    max-width: 280px;
  }

  :deep(.login-btn) {
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .top-header {
    top: 20px;
  }

  .login-form-container {
    margin-top: 40px;
  }

  .login-form {
    padding: 25px 15px;
  }

  .header-info {
    display: none;
  }

  .input-group {
    max-width: 260px;
  }

  :deep(.login-btn) {
    max-width: 260px;
  }

  .form-title {
    font-size: 22px;
  }
}
</style>