/**
 * 用户状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { storage } from '@/utils'
import { login, getUserInfo, logout } from '@/api/auth'
import type { User, UserLoginRequest } from '@/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const userInfo = computed(() => user.value)

  // 初始化用户信息
  function initUserInfo() {
    const savedToken = storage.get(import.meta.env.VITE_APP_TOKEN_KEY || 'token')
    const savedUser = storage.get(import.meta.env.VITE_APP_USER_KEY || 'user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = savedUser
      roles.value = [savedUser.role]
    }
  }

  // 登录
  async function loginAction(loginData: UserLoginRequest) {
    try {
      const response = await login(loginData)
      const { user: userInfo, token: userToken } = response.data

      // 保存用户信息
      token.value = userToken
      user.value = userInfo
      roles.value = [userInfo.role]

      // 持久化存储
      storage.set(import.meta.env.VITE_APP_TOKEN_KEY || 'token', userToken)
      storage.set(import.meta.env.VITE_APP_USER_KEY || 'user', userInfo)

      ElMessage.success('登录成功')
      return Promise.resolve(response)
    } catch (error) {
      ElMessage.error('登录失败')
      return Promise.reject(error)
    }
  }

  // 获取用户信息
  async function fetchUserInfo() {
    try {
      const response = await getUserInfo()
      const userInfo = response.data

      user.value = userInfo
      roles.value = [userInfo.role]

      // 更新存储
      storage.set(import.meta.env.VITE_APP_USER_KEY || 'user', userInfo)

      return Promise.resolve(userInfo)
    } catch (error) {
      // 如果获取用户信息失败，清除本地数据
      await logoutAction()
      return Promise.reject(error)
    }
  }

  // 退出登录
  async function logoutAction() {
    try {
      // 调用退出登录接口
      await logout()
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // 清除状态
      user.value = null
      token.value = ''
      permissions.value = []
      roles.value = []

      // 清除存储
      storage.remove(import.meta.env.VITE_APP_TOKEN_KEY || 'token')
      storage.remove(import.meta.env.VITE_APP_USER_KEY || 'user')

      ElMessage.success('退出登录成功')
    }
  }

  // 更新用户信息
  function updateUserInfo(newUserInfo: Partial<User>) {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo }
      storage.set(import.meta.env.VITE_APP_USER_KEY || 'user', user.value)
    }
  }

  // 检查权限
  function hasPermission(permission: string): boolean {
    return permissions.value.includes(permission)
  }

  // 检查角色
  function hasRole(role: string): boolean {
    return roles.value.includes(role)
  }

  // 检查是否有任一权限
  function hasAnyPermission(permissionList: string[]): boolean {
    return permissionList.some(permission => hasPermission(permission))
  }

  // 检查是否有任一角色
  function hasAnyRole(roleList: string[]): boolean {
    return roleList.some(role => hasRole(role))
  }

  return {
    // 状态
    user,
    token,
    permissions,
    roles,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    userInfo,
    
    // 方法
    initUserInfo,
    loginAction,
    fetchUserInfo,
    logoutAction,
    updateUserInfo,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole
  }
})
