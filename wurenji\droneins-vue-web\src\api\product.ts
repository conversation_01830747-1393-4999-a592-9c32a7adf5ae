/**
 * 产品管理API
 */
import { get, post, put, del } from '@/utils/request'
import type { PageData } from './user'

// 定义类型
export interface Product {
  id: number
  name: string
  description: string
  price: number
  stock: number
  categoryId: number
  category?: Category
  images: string[]
  status: number
  createdAt: string
  updatedAt: string
}

export interface ProductCreateRequest {
  name: string
  description: string
  price: number
  stock: number
  categoryId: number
  images: string[]
}

export interface ProductUpdateRequest {
  name?: string
  description?: string
  price?: number
  stock?: number
  categoryId?: number
  images?: string[]
  status?: number
}

export interface ProductListQuery {
  page?: number
  pageSize?: number
  keyword?: string
  categoryId?: number
  status?: number
}

export interface Category {
  id: number
  name: string
  description?: string
  parentId?: number
  sort: number
  status: number
}

/**
 * 获取产品列表
 */
export function getProductList(params: ProductListQuery) {
  return get<PageData<Product>>('/products', params)
}

/**
 * 获取产品详情
 */
export function getProductDetail(id: number) {
  return get<Product>(`/products/${id}`)
}

/**
 * 创建产品
 */
export function createProduct(data: ProductCreateRequest) {
  return post<Product>('/products', data)
}

/**
 * 更新产品
 */
export function updateProduct(id: number, data: ProductUpdateRequest) {
  return put<Product>(`/products/${id}`, data)
}

/**
 * 删除产品
 */
export function deleteProduct(id: number) {
  return del(`/products/${id}`)
}

/**
 * 获取我的产品
 */
export function getMyProducts(params: ProductListQuery) {
  return get<PageData<Product>>('/products/my', params)
}

/**
 * 根据分类获取产品
 */
export function getProductsByCategory(categoryId: number, params: ProductListQuery) {
  return get<PageData<Product>>(`/products/category/${categoryId}`, params)
}

/**
 * 获取分类列表
 */
export function getCategoryList() {
  return get<Category[]>('/categories')
}
