<template>
  <div class="profile-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>个人资料</span>
        </div>
      </template>

      <div class="profile-content">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <el-avatar :size="120" :src="userStore.user?.avatar" class="avatar">
            {{ userStore.user?.nickname?.charAt(0) || 'U' }}
          </el-avatar>
          <el-button type="primary" size="small" class="upload-btn">
            更换头像
          </el-button>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
            size="large"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" disabled />
            </el-form-item>

            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
            </el-form-item>

            <el-form-item label="手机号" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
            </el-form-item>

            <el-form-item label="角色">
              <el-tag :type="getRoleType(userStore.user?.role)">
                {{ getRoleText(userStore.user?.role) }}
              </el-tag>
            </el-form-item>

            <el-form-item label="注册时间">
              <span>{{ formatDate(userStore.user?.created_at || '') }}</span>
            </el-form-item>

            <el-form-item label="最后登录">
              <span>{{ formatDate(userStore.user?.last_login_at || '') }}</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="loading" @click="handleUpdate">
                保存修改
              </el-button>
              <el-button @click="resetForm">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 修改密码 -->
    <el-card class="password-card">
      <template #header>
        <div class="card-header">
          <span>修改密码</span>
        </div>
      </template>

      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
        size="large"
      >
        <el-form-item label="当前密码" prop="old_password">
          <el-input
            v-model="passwordForm.old_password"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
            修改密码
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { updateProfile, changePassword } from '@/api/user'
import { formatDate, isPhone } from '@/utils'
import type { UserUpdateRequest, UserChangePasswordRequest } from '@/types'

const userStore = useUserStore()

const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const loading = ref(false)
const passwordLoading = ref(false)

// 个人资料表单
const profileForm = reactive({
  username: '',
  email: '',
  nickname: '',
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 验证手机号
const validatePhone = (rule: any, value: any, callback: any) => {
  if (value && !isPhone(value)) {
    callback(new Error('请输入正确的手机号格式'))
  } else {
    callback()
  }
}

// 验证确认密码
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value !== passwordForm.new_password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 个人资料验证规则
const profileRules: FormRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules: FormRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 获取角色类型
function getRoleType(role?: string) {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'user':
      return 'success'
    default:
      return 'info'
  }
}

// 获取角色文本
function getRoleText(role?: string) {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'user':
      return '普通用户'
    default:
      return '未知'
  }
}

// 初始化表单
function initForm() {
  if (userStore.user) {
    profileForm.username = userStore.user.username
    profileForm.email = userStore.user.email
    profileForm.nickname = userStore.user.nickname
    profileForm.phone = userStore.user.phone || ''
  }
}

// 重置表单
function resetForm() {
  initForm()
}

// 更新个人资料
async function handleUpdate() {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    loading.value = true

    const updateData: UserUpdateRequest = {
      nickname: profileForm.nickname,
      phone: profileForm.phone || undefined
    }

    await updateProfile(updateData)
    
    // 更新store中的用户信息
    userStore.updateUserInfo(updateData)
    
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    console.error('Update profile error:', error)
  } finally {
    loading.value = false
  }
}

// 修改密码
async function handleChangePassword() {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    const changeData: UserChangePasswordRequest = {
      old_password: passwordForm.old_password,
      new_password: passwordForm.new_password
    }

    await changePassword(changeData)
    
    ElMessage.success('密码修改成功')
    
    // 清空密码表单
    passwordForm.old_password = ''
    passwordForm.new_password = ''
    passwordForm.confirm_password = ''
  } catch (error) {
    console.error('Change password error:', error)
  } finally {
    passwordLoading.value = false
  }
}

onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-page {
  padding: 24px;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
}

.profile-content {
  display: flex;
  gap: 48px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.avatar {
  background: #1890ff;
}

.upload-btn {
  width: 100px;
}

.form-section {
  flex: 1;
  max-width: 500px;
}

.password-card {
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .form-section {
    max-width: none;
  }
}
</style>
