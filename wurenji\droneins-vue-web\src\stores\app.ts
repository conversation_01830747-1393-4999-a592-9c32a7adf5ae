/**
 * 应用状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { storage } from '@/utils'
import type { MenuItem } from '@/types'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const theme = ref<'light' | 'dark'>('light')
  const language = ref('zh-CN')
  const loading = ref(false)
  const menuList = ref<MenuItem[]>([])
  const breadcrumbs = ref<Array<{ title: string; path?: string }>>([])
  const cachedViews = ref<string[]>([])

  // 计算属性
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')

  // 初始化应用设置
  function initAppSettings() {
    const savedCollapsed = storage.get('sidebar_collapsed')
    const savedTheme = storage.get('theme')
    const savedLanguage = storage.get('language')

    if (savedCollapsed !== null) {
      sidebarCollapsed.value = savedCollapsed
    }
    if (savedTheme) {
      theme.value = savedTheme
    }
    if (savedLanguage) {
      language.value = savedLanguage
    }
  }

  // 切换侧边栏
  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value
    storage.set('sidebar_collapsed', sidebarCollapsed.value)
  }

  // 设置设备类型
  function setDevice(deviceType: 'desktop' | 'tablet' | 'mobile') {
    device.value = deviceType
    
    // 移动端自动收起侧边栏
    if (deviceType === 'mobile') {
      sidebarCollapsed.value = true
    }
  }

  // 切换主题
  function toggleTheme() {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    storage.set('theme', theme.value)
    
    // 应用主题到document
    document.documentElement.setAttribute('data-theme', theme.value)
  }

  // 设置主题
  function setTheme(newTheme: 'light' | 'dark') {
    theme.value = newTheme
    storage.set('theme', newTheme)
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  // 设置语言
  function setLanguage(lang: string) {
    language.value = lang
    storage.set('language', lang)
  }

  // 设置加载状态
  function setLoading(status: boolean) {
    loading.value = status
  }

  // 设置菜单列表
  function setMenuList(menus: MenuItem[]) {
    menuList.value = menus
  }

  // 设置面包屑
  function setBreadcrumbs(crumbs: Array<{ title: string; path?: string }>) {
    breadcrumbs.value = crumbs
  }

  // 添加缓存视图
  function addCachedView(viewName: string) {
    if (!cachedViews.value.includes(viewName)) {
      cachedViews.value.push(viewName)
    }
  }

  // 移除缓存视图
  function removeCachedView(viewName: string) {
    const index = cachedViews.value.indexOf(viewName)
    if (index > -1) {
      cachedViews.value.splice(index, 1)
    }
  }

  // 清除所有缓存视图
  function clearCachedViews() {
    cachedViews.value = []
  }

  // 重置应用状态
  function resetAppState() {
    sidebarCollapsed.value = false
    device.value = 'desktop'
    theme.value = 'light'
    language.value = 'zh-CN'
    loading.value = false
    menuList.value = []
    breadcrumbs.value = []
    cachedViews.value = []
  }

  return {
    // 状态
    sidebarCollapsed,
    device,
    theme,
    language,
    loading,
    menuList,
    breadcrumbs,
    cachedViews,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    initAppSettings,
    toggleSidebar,
    setDevice,
    toggleTheme,
    setTheme,
    setLanguage,
    setLoading,
    setMenuList,
    setBreadcrumbs,
    addCachedView,
    removeCachedView,
    clearCachedViews,
    resetAppState
  }
})
