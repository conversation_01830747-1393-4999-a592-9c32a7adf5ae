<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item
      v-for="(item, index) in appStore.breadcrumbs"
      :key="index"
      :to="item.path"
    >
      {{ item.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
</script>

<style scoped>
:deep(.el-breadcrumb__inner) {
  color: #666;
}

:deep(.el-breadcrumb__inner.is-link) {
  color: #1890ff;
}

:deep(.el-breadcrumb__inner.is-link:hover) {
  color: #40a9ff;
}
</style>
