<template>
  <div class="product-edit-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>编辑产品</span>
        </div>
      </template>

      <div class="placeholder">
        <el-result
          icon="warning"
          title="功能开发中"
          sub-title="产品编辑功能正在开发中，敬请期待"
        >
          <template #extra>
            <el-button type="primary" @click="$router.go(-1)">返回</el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 产品编辑页面 - 待实现
</script>

<style scoped>
.product-edit-page {
  padding: 24px;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
}

.placeholder {
  padding: 40px;
  text-align: center;
}
</style>
