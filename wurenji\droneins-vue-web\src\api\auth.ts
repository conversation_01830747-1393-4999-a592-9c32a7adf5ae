/**
 * 认证相关API
 */
import { post, get } from '@/utils/request'

// 定义类型
export interface UserLoginRequest {
  username: string
  password: string
}

export interface UserLoginResponse {
  token: string
  user: User
}

export interface UserCreateRequest {
  username: string
  password: string
  email: string
  nickname?: string
}

export interface User {
  id: number
  username: string
  nickname: string
  email: string
  avatar?: string
  role: string
}

/**
 * 用户登录
 */
export function login(data: UserLoginRequest) {
  return post<UserLoginResponse>('/auth/login', data)
}

/**
 * 用户注册
 */
export function register(data: UserCreateRequest) {
  return post<User>('/auth/register', data)
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return get<User>('/users/profile')
}

/**
 * 刷新token
 */
export function refreshToken() {
  return post<{ token: string }>('/auth/refresh')
}

/**
 * 退出登录
 */
export function logout() {
  return post('/auth/logout')
}
