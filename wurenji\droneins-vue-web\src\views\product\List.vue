<template>
  <div class="product-list-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <el-button type="primary" @click="$router.push('/product/create')">
            创建产品
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="产品名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入产品名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable>
              <el-option
                v-for="category in categoryList"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="上架" :value="1" />
              <el-option label="下架" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="productList"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="产品名称" width="200" />
        <el-table-column prop="price" label="价格" width="120">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="100" />
        <el-table-column prop="category.name" label="分类" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="user.nickname" label="创建者" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '下架' : '上架' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductList, updateProductStatus, deleteProduct, getCategoryList } from '@/api/product'
import { formatDate } from '@/utils'
import type { Product, Category, ProductListQuery } from '@/types'

const router = useRouter()
const loading = ref(false)
const productList = ref<Product[]>([])
const categoryList = ref<Category[]>([])

// 搜索表单
const searchForm = reactive({
  name: '',
  category_id: undefined as number | undefined,
  status: undefined as number | undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0
})

// 获取产品列表
async function fetchProductList() {
  try {
    loading.value = true
    
    const params: ProductListQuery = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchForm
    }

    // 清空空值
    Object.keys(params).forEach(key => {
      const value = params[key as keyof ProductListQuery]
      if (value === '' || value === undefined) {
        delete params[key as keyof ProductListQuery]
      }
    })

    const response = await getProductList(params)
    const data = response.data

    productList.value = data.list
    pagination.total = data.total
  } catch (error) {
    console.error('Fetch product list error:', error)
  } finally {
    loading.value = false
  }
}

// 获取分类列表
async function fetchCategoryList() {
  try {
    const response = await getCategoryList()
    categoryList.value = response.data
  } catch (error) {
    console.error('Fetch category list error:', error)
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  fetchProductList()
}

// 重置
function handleReset() {
  searchForm.name = ''
  searchForm.category_id = undefined
  searchForm.status = undefined
  pagination.page = 1
  fetchProductList()
}

// 查看产品
function handleView(product: Product) {
  router.push(`/product/detail/${product.id}`)
}

// 编辑产品
function handleEdit(product: Product) {
  router.push(`/product/edit/${product.id}`)
}

// 切换产品状态
async function handleToggleStatus(product: Product) {
  try {
    const newStatus = product.status === 1 ? 0 : 1
    const action = newStatus === 1 ? '上架' : '下架'
    
    await ElMessageBox.confirm(`确定要${action}产品 "${product.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateProductStatus(product.id, newStatus)
    
    product.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle product status error:', error)
    }
  }
}

// 删除产品
async function handleDelete(product: Product) {
  try {
    await ElMessageBox.confirm(`确定要删除产品 "${product.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteProduct(product.id)
    
    ElMessage.success('删除成功')
    fetchProductList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete product error:', error)
    }
  }
}

// 分页大小改变
function handleSizeChange(size: number) {
  pagination.page_size = size
  pagination.page = 1
  fetchProductList()
}

// 当前页改变
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchProductList()
}

onMounted(() => {
  fetchCategoryList()
  fetchProductList()
})
</script>

<style scoped>
.product-list-page {
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.search-section {
  margin-bottom: 24px;
}

.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}
</style>
