<template>
  <div class="dashboard">
    <!-- 退出登录按钮 -->
    <div class="logout-container">
      <el-button type="primary" @click="handleLogout" class="logout-btn">
        退出登录
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 卡片滑动区域 -->
      <div class="cards-container">
        <div class="cards-wrapper" ref="cardsWrapper">
          <div
            v-for="(module, index) in modules"
            :key="module.id"
            class="module-card"
            :class="{ 'active': index === activeIndex }"
            @click="selectCard(index)"
          >
            <div class="card-content">
              <div class="card-icon">
                <div class="icon-diamond">
                  <div class="icon-inner">
                    <i :class="module.icon"></i>
                  </div>
                </div>
              </div>
              <h3 class="card-title">{{ module.title }}</h3>
              <div class="card-button" v-if="index === activeIndex">
                <button class="enter-btn">进入</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部导航文字 -->
      <div class="bottom-navigation">
        <div
          v-for="(module, index) in modules"
          :key="module.id"
          class="nav-item"
          :class="{ 'active': index === activeIndex }"
          @click="selectCard(index)"
        >
          {{ module.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前激活的卡片索引
const activeIndex = ref(3) // 默认选中中间的卡片

// 卡片容器引用
const cardsWrapper = ref<HTMLElement>()

// 模块数据
const modules = ref([
  {
    id: 1,
    title: '无人机管理',
    icon: 'fas fa-helicopter'
  },
  {
    id: 2,
    title: '任务规划',
    icon: 'fas fa-route'
  },
  {
    id: 3,
    title: '数据采集',
    icon: 'fas fa-camera'
  },
  {
    id: 4,
    title: '智能分析',
    icon: 'fas fa-brain'
  },
  {
    id: 5,
    title: '数据报告',
    icon: 'fas fa-chart-line'
  },
  {
    id: 6,
    title: '系统设置',
    icon: 'fas fa-cog'
  },
  {
    id: 7,
    title: '用户管理',
    icon: 'fas fa-users'
  },
  {
    id: 8,
    title: '监控中心',
    icon: 'fas fa-desktop'
  }
])

// 选择卡片
function selectCard(index: number) {
  activeIndex.value = index
  updateCardsPosition()
}

// 更新卡片位置
function updateCardsPosition() {
  if (!cardsWrapper.value) return

  const cards = cardsWrapper.value.children
  const totalCards = cards.length
  const centerIndex = activeIndex.value

  for (let i = 0; i < totalCards; i++) {
    const card = cards[i] as HTMLElement
    const offset = i - centerIndex
    const absOffset = Math.abs(offset)

    // 计算位置、缩放和3D效果
    const translateX = offset * 220 // 增加卡片间距
    const translateY = absOffset * 20 // 添加垂直偏移增强层次感
    const translateZ = -absOffset * 100 // 3D深度效果
    const rotateY = offset * 15 // 添加Y轴旋转
    const scale = i === centerIndex ? 1.15 : Math.max(0.7, 1 - absOffset * 0.15)

    // 更精细的层级控制
    let zIndex = 10 - absOffset
    if (i === centerIndex) zIndex = 20

    // 更平滑的透明度过渡
    let opacity = 1
    if (absOffset > 2) {
      opacity = Math.max(0.2, 1 - (absOffset - 2) * 0.3)
    } else if (absOffset > 0) {
      opacity = Math.max(0.6, 1 - absOffset * 0.2)
    }

    // 应用3D变换
    card.style.transform = `
      translateX(${translateX}px)
      translateY(${translateY}px)
      translateZ(${translateZ}px)
      rotateY(${rotateY}deg)
      scale(${scale})
    `
    card.style.zIndex = zIndex.toString()
    card.style.opacity = opacity.toString()

    // 添加模糊效果增强深度感
    const blurAmount = absOffset > 0 ? Math.min(absOffset * 2, 8) : 0
    card.style.filter = `blur(${blurAmount}px)`
  }
}

// 退出登录
function handleLogout() {
  sessionStorage.removeItem('isLoggedIn')
  sessionStorage.removeItem('userInfo')
  ElMessage.success('已安全退出系统')
  router.push('/login')
}

onMounted(() => {
  updateCardsPosition()
})
</script>

<style scoped>
.dashboard {
  width: 100vw;
  height: 100vh;
  background: radial-gradient(ellipse at center, #0a1a2e 0%, #0a0a0a 70%);
  color: white;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

/* 背景粒子效果 */
.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #00d4ff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, #00d4ff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, #00d4ff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 20s linear infinite;
  opacity: 0.3;
  z-index: 1;
}

@keyframes particleFloat {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* 退出登录按钮 */
.logout-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
}

.logout-btn {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2vh 2vw;
  box-sizing: border-box;
}

/* 卡片容器 */
.cards-container {
  width: 100%;
  height: 60vh;
  max-height: 500px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1500px; /* 增强3D透视效果 */
  perspective-origin: center center;
  margin-bottom: 8vh;
  overflow: visible; /* 确保3D效果可见 */
}

.cards-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d; /* 保持3D变换 */
}

/* 模块卡片 */
.module-card {
  position: absolute;
  width: min(280px, 25vw);
  height: min(350px, 45vh);
  max-width: 320px;
  max-height: 400px;
  min-width: 200px;
  min-height: 250px;
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.12),
    rgba(0, 150, 200, 0.08),
    rgba(0, 100, 150, 0.05)
  );
  border: 2px solid rgba(0, 212, 255, 0.3);
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(25px);
  transform-style: preserve-3d;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 212, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 212, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* 卡片内部光效 */
.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 212, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
  z-index: 1;
}

.module-card:hover::before {
  left: 100%;
}

.module-card.active {
  border-color: rgba(0, 212, 255, 0.9);
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.18),
    rgba(0, 150, 200, 0.12),
    rgba(0, 100, 150, 0.08)
  );
  box-shadow:
    0 25px 80px rgba(0, 212, 255, 0.4),
    0 10px 30px rgba(0, 212, 255, 0.3),
    0 0 50px rgba(0, 212, 255, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.25),
    inset 0 -2px 0 rgba(0, 212, 255, 0.2);
  transform: translateZ(20px) !important; /* 突出激活卡片 */
}

/* 非激活卡片的暗化效果 */
.module-card:not(.active) {
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.06),
    rgba(0, 150, 200, 0.04),
    rgba(0, 100, 150, 0.02)
  );
  border-color: rgba(0, 212, 255, 0.2);
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 30px 20px;
  text-align: center;
  position: relative;
  z-index: 2;
  transform-style: preserve-3d;
}

/* 卡片图标 */
.card-icon {
  margin-bottom: 30px;
}

.icon-diamond {
  width: 120px;
  height: 120px;
  position: relative;
  transform: rotate(45deg);
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.25),
    rgba(0, 150, 200, 0.15),
    rgba(0, 100, 150, 0.1)
  );
  border: 2px solid rgba(0, 212, 255, 0.5);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  box-shadow:
    0 0 35px rgba(0, 212, 255, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 0 25px rgba(0, 212, 255, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  transform-style: preserve-3d;
}

/* 图标钻石悬浮效果 */
.module-card:hover .icon-diamond {
  transform: rotate(45deg) translateZ(10px);
  box-shadow:
    0 0 45px rgba(0, 212, 255, 0.6),
    0 12px 30px rgba(0, 0, 0, 0.4),
    inset 0 0 35px rgba(0, 212, 255, 0.2);
}

.icon-inner {
  transform: rotate(-45deg);
  font-size: 36px;
  color: #00d4ff;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  transition: all 0.4s ease;
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

.module-card.active .icon-diamond {
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.4),
    rgba(0, 150, 200, 0.3),
    rgba(0, 100, 150, 0.2)
  );
  border-color: rgba(0, 212, 255, 0.9);
  box-shadow:
    0 0 50px rgba(0, 212, 255, 0.7),
    0 12px 25px rgba(0, 0, 0, 0.4),
    inset 0 0 40px rgba(0, 212, 255, 0.3),
    inset 0 3px 0 rgba(255, 255, 255, 0.2);
  transform: rotate(45deg) translateZ(15px);
}

.module-card.active .icon-inner {
  color: #ffffff;
  text-shadow: 0 0 35px rgba(0, 212, 255, 1);
  filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.8));
  transform: rotate(-45deg) scale(1.1);
}

/* 卡片标题 */
.card-title {
  font-size: 20px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 20px 0;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  transition: all 0.4s ease;
  letter-spacing: 0.5px;
}

.module-card.active .card-title {
  color: #00d4ff;
  text-shadow:
    0 0 25px rgba(0, 212, 255, 0.9),
    0 0 50px rgba(0, 212, 255, 0.5);
  transform: translateZ(5px);
}

/* 非激活卡片标题暗化 */
.module-card:not(.active) .card-title {
  color: rgba(255, 255, 255, 0.6);
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

/* 进入按钮 */
.card-button {
  margin-top: 20px;
  transform-style: preserve-3d;
}

.enter-btn {
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.25),
    rgba(0, 150, 200, 0.35),
    rgba(0, 100, 150, 0.2)
  );
  border: 2px solid rgba(0, 212, 255, 0.6);
  color: #00d4ff;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(15px);
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.7);
  box-shadow:
    0 6px 20px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.enter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.enter-btn:hover {
  background: linear-gradient(145deg,
    rgba(0, 212, 255, 0.4),
    rgba(0, 150, 200, 0.5),
    rgba(0, 100, 150, 0.3)
  );
  border-color: rgba(0, 212, 255, 0.9);
  box-shadow:
    0 8px 35px rgba(0, 212, 255, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) translateZ(5px);
  text-shadow: 0 0 20px rgba(0, 212, 255, 1);
}

.enter-btn:hover::before {
  left: 100%;
}

/* 底部导航 */
.bottom-navigation {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  max-width: 1200px;
}

.nav-item {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 20px;
  border: 1px solid transparent;
  backdrop-filter: blur(10px);
}

.nav-item:hover {
  color: rgba(0, 212, 255, 0.8);
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

.nav-item.active {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.15);
  border-color: rgba(0, 212, 255, 0.4);
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

/* 添加浮动动画增强层次感 */
@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.module-card.active {
  animation: cardFloat 4s ease-in-out infinite;
}

/* 添加脉冲光效 */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 25px 80px rgba(0, 212, 255, 0.4),
      0 10px 30px rgba(0, 212, 255, 0.3),
      0 0 50px rgba(0, 212, 255, 0.25),
      inset 0 2px 0 rgba(255, 255, 255, 0.25),
      inset 0 -2px 0 rgba(0, 212, 255, 0.2);
  }
  50% {
    box-shadow:
      0 30px 100px rgba(0, 212, 255, 0.6),
      0 15px 40px rgba(0, 212, 255, 0.4),
      0 0 70px rgba(0, 212, 255, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 212, 255, 0.3);
  }
}

.module-card.active {
  animation: cardFloat 4s ease-in-out infinite, pulseGlow 3s ease-in-out infinite;
}

/* 背景粒子增强 */
.dashboard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 25px 25px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(1px 1px at 75px 75px, rgba(0, 212, 255, 0.2), transparent),
    radial-gradient(2px 2px at 125px 125px, rgba(0, 212, 255, 0.1), transparent);
  background-size: 150px 150px;
  animation: particleFloat 25s linear infinite reverse;
  opacity: 0.4;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .cards-container {
    height: 350px;
  }

  .module-card {
    width: 240px;
    height: 300px;
  }

  .icon-diamond {
    width: 100px;
    height: 100px;
  }

  .icon-inner {
    font-size: 30px;
  }

  .bottom-navigation {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 10px;
  }

  .cards-container {
    height: 300px;
    margin-bottom: 40px;
  }

  .module-card {
    width: 200px;
    height: 260px;
  }

  .icon-diamond {
    width: 80px;
    height: 80px;
  }

  .icon-inner {
    font-size: 24px;
  }

  .card-title {
    font-size: 18px;
  }

  .bottom-navigation {
    gap: 20px;
  }

  .nav-item {
    font-size: 14px;
    padding: 8px 16px;
  }
}

@media (max-width: 480px) {
  .bottom-navigation {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .logout-container {
    top: 10px;
    right: 10px;
  }

  .logout-btn {
    padding: 6px 16px;
    font-size: 12px;
  }
}
</style>
