<template>
  <div class="dashboard">
    <!-- 科技背景网格 -->
    <div class="tech-grid"></div>
    <div class="tech-particles"></div>

    <!-- 退出登录按钮 -->
    <div class="logout-container">
      <button @click="handleLogout" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
        <span>退出登录</span>
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标题区域 -->
      <div class="header-section">
        <h1 class="main-title">无人机智能管控系统</h1>
        <div class="title-line"></div>
      </div>

      <!-- 卡片滑动区域 -->
      <div class="cards-container">
        <div class="cards-wrapper" ref="cardsWrapper">
          <div
            v-for="(module, index) in modules"
            :key="module.id"
            class="module-card"
            :class="{ 'active': index === activeIndex }"
            @click="selectCard(index)"
          >
            <div class="card-border"></div>
            <div class="card-content">
              <div class="card-icon">
                <div class="icon-container">
                  <i :class="module.icon"></i>
                </div>
                <div class="icon-glow"></div>
              </div>
              <h3 class="card-title">{{ module.title }}</h3>
              <div class="card-description">{{ module.description }}</div>
              <div class="card-button" v-if="index === activeIndex">
                <button class="enter-btn" @click.stop="enterModule(module)">
                  <span>进入</span>
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
            <div class="card-reflection"></div>
          </div>
        </div>
      </div>

      <!-- 底部指示器 -->
      <div class="bottom-indicators">
        <div
          v-for="(module, index) in modules"
          :key="module.id"
          class="indicator"
          :class="{ 'active': index === activeIndex }"
          @click="selectCard(index)"
        >
          <div class="indicator-dot"></div>
          <span class="indicator-label">{{ module.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前激活的卡片索引
const activeIndex = ref(3) // 默认选中中间的卡片

// 卡片容器引用
const cardsWrapper = ref<HTMLElement>()

// 模块数据
const modules = ref([
  {
    id: 1,
    title: '无人机管理',
    description: '设备状态监控与管理',
    icon: 'fas fa-helicopter'
  },
  {
    id: 2,
    title: '任务规划',
    description: '智能路径规划系统',
    icon: 'fas fa-route'
  },
  {
    id: 3,
    title: '数据采集',
    description: '多维度数据收集',
    icon: 'fas fa-camera'
  },
  {
    id: 4,
    title: '智能分析',
    description: 'AI驱动数据分析',
    icon: 'fas fa-brain'
  },
  {
    id: 5,
    title: '数据报告',
    description: '可视化报表生成',
    icon: 'fas fa-chart-line'
  },
  {
    id: 6,
    title: '系统设置',
    description: '系统参数配置',
    icon: 'fas fa-cog'
  },
  {
    id: 7,
    title: '用户管理',
    description: '权限与用户控制',
    icon: 'fas fa-users'
  },
  {
    id: 8,
    title: '监控中心',
    description: '实时状态监控',
    icon: 'fas fa-desktop'
  }
])

// 进入模块
function enterModule(module: any) {
  ElMessage.success(`正在进入${module.title}模块...`)
  // 这里可以添加路由跳转逻辑
  // router.push(`/${module.route}`)
}

// 选择卡片
function selectCard(index: number) {
  activeIndex.value = index
  updateCardsPosition()
}

// 更新卡片位置
function updateCardsPosition() {
  if (!cardsWrapper.value) return

  const cards = cardsWrapper.value.children
  const totalCards = cards.length
  const centerIndex = activeIndex.value

  for (let i = 0; i < totalCards; i++) {
    const card = cards[i] as HTMLElement
    const offset = i - centerIndex
    const absOffset = Math.abs(offset)

    // 计算位置、缩放和3D效果
    const translateX = offset * 220 // 增加卡片间距
    const translateY = absOffset * 20 // 添加垂直偏移增强层次感
    const translateZ = -absOffset * 100 // 3D深度效果
    const rotateY = offset * 15 // 添加Y轴旋转
    const scale = i === centerIndex ? 1.15 : Math.max(0.7, 1 - absOffset * 0.15)

    // 更精细的层级控制
    let zIndex = 10 - absOffset
    if (i === centerIndex) zIndex = 20

    // 更平滑的透明度过渡
    let opacity = 1
    if (absOffset > 2) {
      opacity = Math.max(0.2, 1 - (absOffset - 2) * 0.3)
    } else if (absOffset > 0) {
      opacity = Math.max(0.6, 1 - absOffset * 0.2)
    }

    // 应用3D变换
    card.style.transform = `
      translateX(${translateX}px)
      translateY(${translateY}px)
      translateZ(${translateZ}px)
      rotateY(${rotateY}deg)
      scale(${scale})
    `
    card.style.zIndex = zIndex.toString()
    card.style.opacity = opacity.toString()

    // 添加模糊效果增强深度感
    const blurAmount = absOffset > 0 ? Math.min(absOffset * 2, 8) : 0
    card.style.filter = `blur(${blurAmount}px)`
  }
}

// 退出登录
function handleLogout() {
  sessionStorage.removeItem('isLoggedIn')
  sessionStorage.removeItem('userInfo')
  ElMessage.success('已安全退出系统')
  router.push('/login')
}

onMounted(() => {
  updateCardsPosition()
})
</script>

<style scoped>
.dashboard {
  width: 100vw;
  height: 100vh;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(0, 200, 255, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 科技网格背景 */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  opacity: 0.3;
  z-index: 1;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 科技粒子效果 */
.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, #00d4ff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, #00ffff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 25s linear infinite;
  opacity: 0.4;
  z-index: 1;
}

@keyframes particleFloat {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-100px) rotate(360deg); }
}

/* 退出登录按钮 */
.logout-container {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 100;
}

.logout-btn {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.logout-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

.logout-btn i {
  font-size: 16px;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2vh 2vw;
  box-sizing: border-box;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 60px;
  z-index: 3;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: #ffffff;
  margin: 0;
  text-shadow:
    0 0 20px rgba(0, 255, 255, 0.5),
    0 0 40px rgba(0, 255, 255, 0.3);
  letter-spacing: 3px;
  background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 20px rgba(0, 255, 255, 0.5),
      0 0 40px rgba(0, 255, 255, 0.3);
  }
  100% {
    text-shadow:
      0 0 30px rgba(0, 255, 255, 0.8),
      0 0 60px rgba(0, 255, 255, 0.5);
  }
}

.title-line {
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  margin: 20px auto;
  border-radius: 1px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 卡片容器 */
.cards-container {
  width: 100%;
  height: 50vh;
  max-height: 400px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 2000px;
  perspective-origin: center center;
  margin-bottom: 60px;
  overflow: visible;
}

.cards-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
}

/* 模块卡片 */
.module-card {
  position: absolute;
  width: 280px;
  height: 320px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  transform-style: preserve-3d;
  overflow: hidden;
  position: relative;
}

/* 卡片边框发光效果 */
.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(0, 255, 255, 0.1) 25%,
    transparent 50%,
    rgba(0, 255, 255, 0.1) 75%,
    rgba(0, 255, 255, 0.3) 100%
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.6s ease;
}

.module-card.active .card-border {
  opacity: 1;
  animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.3) 0%,
      rgba(0, 255, 255, 0.1) 25%,
      transparent 50%,
      rgba(0, 255, 255, 0.1) 75%,
      rgba(0, 255, 255, 0.3) 100%
    );
  }
  100% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.6) 0%,
      rgba(0, 255, 255, 0.3) 25%,
      rgba(0, 255, 255, 0.1) 50%,
      rgba(0, 255, 255, 0.3) 75%,
      rgba(0, 255, 255, 0.6) 100%
    );
  }
}

.module-card.active {
  background: rgba(0, 0, 0, 0.6);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 20px 60px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateZ(30px) scale(1.05) !important;
}

.module-card:not(.active) {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 255, 255, 0.1);
}

/* 卡片反射效果 */
.card-reflection {
  position: absolute;
  bottom: -100%;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 50%
  );
  transform: scaleY(-1);
  opacity: 0.3;
  pointer-events: none;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 30px 20px;
  text-align: center;
  position: relative;
  z-index: 2;
}

/* 卡片图标 */
.card-icon {
  position: relative;
  margin-bottom: 20px;
}

.icon-container {
  width: 80px;
  height: 80px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.1);
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
}

.icon-container i {
  font-size: 32px;
  color: #00ffff;
  transition: all 0.4s ease;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.4s ease;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

.module-card.active .icon-container {
  border-color: rgba(0, 255, 255, 0.8);
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.module-card.active .icon-container i {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 1);
}

.module-card.active .icon-glow {
  opacity: 0.8;
}

/* 卡片标题 */
.card-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 10px 0;
  transition: all 0.4s ease;
  letter-spacing: 1px;
}

.card-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
  line-height: 1.4;
  transition: all 0.4s ease;
}

.module-card.active .card-title {
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

.module-card.active .card-description {
  color: rgba(255, 255, 255, 0.8);
}

/* 进入按钮 */
.card-button {
  width: 100%;
}

.enter-btn {
  width: 100%;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.enter-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.4);
  transform: translateY(-2px);
}

.enter-btn i {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.enter-btn:hover i {
  transform: translateX(3px);
}

/* 底部指示器 */
.bottom-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
  max-width: 1200px;
  margin-top: 40px;
}

.indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: 10px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.indicator-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.indicator-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  text-align: center;
  letter-spacing: 0.5px;
}

.indicator.active .indicator-dot {
  background: #00ffff;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
}

.indicator.active .indicator-dot::before {
  background: rgba(0, 255, 255, 0.3);
  width: 20px;
  height: 20px;
}

.indicator.active .indicator-label {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.indicator:hover .indicator-label {
  color: rgba(0, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-title {
    font-size: 3rem;
  }

  .module-card {
    width: 260px;
    height: 300px;
  }

  .bottom-indicators {
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .cards-container {
    height: 40vh;
    min-height: 280px;
  }

  .module-card {
    width: 240px;
    height: 280px;
  }

  .icon-container {
    width: 60px;
    height: 60px;
  }

  .icon-container i {
    font-size: 24px;
  }

  .card-title {
    font-size: 16px;
  }

  .card-description {
    font-size: 11px;
  }

  .bottom-indicators {
    gap: 20px;
    margin-top: 30px;
  }

  .indicator-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .header-section {
    margin-bottom: 40px;
  }

  .cards-container {
    height: 35vh;
    min-height: 250px;
  }

  .module-card {
    width: 200px;
    height: 240px;
  }

  .card-content {
    padding: 20px 15px;
  }

  .bottom-indicators {
    flex-direction: column;
    gap: 15px;
  }

  .indicator {
    flex-direction: row;
    gap: 10px;
  }

  .logout-container {
    top: 20px;
    right: 20px;
  }

  .logout-btn {
    padding: 10px 16px;
    font-size: 12px;
  }
}

/* 添加浮动动画增强层次感 */
@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.module-card.active {
  animation: cardFloat 4s ease-in-out infinite;
}

/* 添加脉冲光效 */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 25px 80px rgba(0, 212, 255, 0.4),
      0 10px 30px rgba(0, 212, 255, 0.3),
      0 0 50px rgba(0, 212, 255, 0.25),
      inset 0 2px 0 rgba(255, 255, 255, 0.25),
      inset 0 -2px 0 rgba(0, 212, 255, 0.2);
  }
  50% {
    box-shadow:
      0 30px 100px rgba(0, 212, 255, 0.6),
      0 15px 40px rgba(0, 212, 255, 0.4),
      0 0 70px rgba(0, 212, 255, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 212, 255, 0.3);
  }
}

.module-card.active {
  animation: cardFloat 4s ease-in-out infinite, pulseGlow 3s ease-in-out infinite;
}

/* 背景粒子增强 */
.dashboard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 25px 25px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(1px 1px at 75px 75px, rgba(0, 212, 255, 0.2), transparent),
    radial-gradient(2px 2px at 125px 125px, rgba(0, 212, 255, 0.1), transparent);
  background-size: 150px 150px;
  animation: particleFloat 25s linear infinite reverse;
  opacity: 0.4;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .cards-container {
    height: 350px;
  }

  .module-card {
    width: 240px;
    height: 300px;
  }

  .icon-diamond {
    width: 100px;
    height: 100px;
  }

  .icon-inner {
    font-size: 30px;
  }

  .bottom-navigation {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 10px;
  }

  .cards-container {
    height: 300px;
    margin-bottom: 40px;
  }

  .module-card {
    width: 200px;
    height: 260px;
  }

  .icon-diamond {
    width: 80px;
    height: 80px;
  }

  .icon-inner {
    font-size: 24px;
  }

  .card-title {
    font-size: 18px;
  }

  .bottom-navigation {
    gap: 20px;
  }

  .nav-item {
    font-size: 14px;
    padding: 8px 16px;
  }
}

@media (max-width: 480px) {
  .bottom-navigation {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .logout-container {
    top: 10px;
    right: 10px;
  }

  .logout-btn {
    padding: 6px 16px;
    font-size: 12px;
  }
}
</style>
