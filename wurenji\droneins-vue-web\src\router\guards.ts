/**
 * 路由守卫
 */
import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'

/**
 * 设置路由守卫
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, _from, next) => {
    // 如果访问登录页面，直接放行
    if (to.path === '/login') {
      next()
      return
    }

    // 模拟登录状态检查（这里简化处理，实际项目中应该检查token）
    const isLoggedIn = sessionStorage.getItem('isLoggedIn') === 'true'

    // 检查是否需要认证
    if (to.meta.requiresAuth || to.path !== '/login') {
      if (!isLoggedIn) {
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    next()
  })

  // 全局后置守卫
  router.afterEach((to) => {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - DroneIns无人机巡检系统`
    } else {
      document.title = 'DroneIns无人机巡检系统'
    }
  })

  // 路由错误处理
  router.onError((error) => {
    console.error('Router error:', error)
    ElMessage.error('页面加载失败')
  })
}
