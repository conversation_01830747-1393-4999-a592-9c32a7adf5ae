<template>
  <div class="product-create-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>创建产品</span>
        </div>
      </template>

      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        size="large"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="productForm.name"
            placeholder="请输入产品名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            placeholder="请输入产品描述"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品价格" prop="price">
          <el-input-number
            v-model="productForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入产品价格"
            style="width: 200px"
          />
          <span style="margin-left: 8px">元</span>
        </el-form-item>

        <el-form-item label="库存数量" prop="stock">
          <el-input-number
            v-model="productForm.stock"
            :min="0"
            placeholder="请输入库存数量"
            style="width: 200px"
          />
          <span style="margin-left: 8px">件</span>
        </el-form-item>

        <el-form-item label="产品分类" prop="category_id">
          <el-select
            v-model="productForm.category_id"
            placeholder="请选择产品分类"
            style="width: 200px"
          >
            <el-option
              v-for="category in categoryList"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="产品图片">
          <div class="image-upload">
            <el-upload
              v-model:file-list="fileList"
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :on-change="handleImageChange"
              :on-remove="handleImageRemove"
              accept="image/*"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item label="产品属性">
          <div class="attributes-section">
            <div
              v-for="(attr, index) in productForm.attributes"
              :key="index"
              class="attribute-item"
            >
              <el-input
                v-model="attr.key"
                placeholder="属性名"
                style="width: 150px"
              />
              <el-input
                v-model="attr.value"
                placeholder="属性值"
                style="width: 150px; margin-left: 8px"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeAttribute(index)"
                style="margin-left: 8px"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addAttribute">
              添加属性
            </el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            创建产品
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
          <el-button @click="$router.go(-1)">
            返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { createProduct, getCategoryList } from '@/api/product'
import type { ProductCreateRequest, Category } from '@/types'

const router = useRouter()
const productFormRef = ref<FormInstance>()
const loading = ref(false)
const categoryList = ref<Category[]>([])
const fileList = ref<UploadFile[]>([])

// 产品表单
const productForm = reactive({
  name: '',
  description: '',
  price: 0,
  stock: 0,
  category_id: undefined as number | undefined,
  images: [] as string[],
  attributes: [{ key: '', value: '' }]
})

// 表单验证规则
const productRules: FormRules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '产品名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '产品描述不能超过 500 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入产品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于 0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存不能小于 0', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择产品分类', trigger: 'change' }
  ]
}

// 获取分类列表
async function fetchCategoryList() {
  try {
    const response = await getCategoryList()
    categoryList.value = response.data
  } catch (error) {
    console.error('Fetch category list error:', error)
  }
}

// 处理图片变化
function handleImageChange(file: UploadFile) {
  // 这里应该上传图片到服务器，获取图片URL
  // 暂时使用本地URL作为示例
  if (file.raw) {
    const url = URL.createObjectURL(file.raw)
    productForm.images.push(url)
  }
}

// 处理图片移除
function handleImageRemove(file: UploadFile) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    productForm.images.splice(index, 1)
  }
}

// 添加属性
function addAttribute() {
  productForm.attributes.push({ key: '', value: '' })
}

// 移除属性
function removeAttribute(index: number) {
  if (productForm.attributes.length > 1) {
    productForm.attributes.splice(index, 1)
  }
}

// 提交表单
async function handleSubmit() {
  if (!productFormRef.value) return

  try {
    await productFormRef.value.validate()
    loading.value = true

    // 过滤空属性
    const attributes: Record<string, any> = {}
    productForm.attributes.forEach(attr => {
      if (attr.key && attr.value) {
        attributes[attr.key] = attr.value
      }
    })

    const createData: ProductCreateRequest = {
      name: productForm.name,
      description: productForm.description || undefined,
      price: productForm.price,
      stock: productForm.stock,
      category_id: productForm.category_id!,
      images: productForm.images,
      attributes: Object.keys(attributes).length > 0 ? attributes : undefined
    }

    await createProduct(createData)
    
    ElMessage.success('产品创建成功')
    router.push('/product/list')
  } catch (error) {
    console.error('Create product error:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
function handleReset() {
  if (productFormRef.value) {
    productFormRef.value.resetFields()
  }
  productForm.images = []
  productForm.attributes = [{ key: '', value: '' }]
  fileList.value = []
}

onMounted(() => {
  fetchCategoryList()
})
</script>

<style scoped>
.product-create-page {
  padding: 24px;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
}

.image-upload {
  width: 100%;
}

.attributes-section {
  width: 100%;
}

.attribute-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style>
