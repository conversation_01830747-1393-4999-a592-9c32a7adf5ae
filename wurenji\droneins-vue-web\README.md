# DroneIns Vue Web

基于 Vue 3 + TypeScript + Element Plus 的现代化前端管理系统

## 🚀 项目特性

### 技术栈
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供类型安全
- **Element Plus** - 基于 Vue 3 的组件库
- **Vue Router 4** - Vue.js 官方路由管理器
- **Pinia** - Vue 的状态管理库
- **Axios** - 基于 Promise 的 HTTP 客户端
- **Vite** - 下一代前端构建工具

### 核心功能
- ✅ **用户认证** - 登录、注册、权限控制
- ✅ **路由管理** - 动态路由、路由守卫、嵌套路由
- ✅ **状态管理** - Pinia 状态管理，支持持久化
- ✅ **HTTP 请求** - Axios 封装，统一错误处理
- ✅ **组件库** - Element Plus 完整集成
- ✅ **响应式布局** - 支持桌面端和移动端
- ✅ **主题切换** - 明暗主题切换
- ✅ **代码规范** - ESLint + Prettier

## 🛠️ 开发指南

### 环境要求
- Node.js >= 16
- pnpm >= 7 (推荐)

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 代码检查
```bash
pnpm lint
```

### 代码格式化
```bash
pnpm format
```

### 类型检查
```bash
pnpm type-check
```

## 🔧 配置说明

### 环境变量
项目支持多环境配置：

- `.env` - 通用配置
- `.env.development` - 开发环境
- `.env.production` - 生产环境

主要配置项：
```bash
# 应用配置
VITE_APP_TITLE=DroneIns管理系统
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_API_TIMEOUT=10000

# 存储配置
VITE_APP_STORAGE_PREFIX=droneins_
VITE_APP_TOKEN_KEY=token
VITE_APP_USER_KEY=user
```

## 📋 功能模块

### 1. 用户认证
- 登录/注册页面
- JWT Token 管理
- 权限控制
- 自动登录

### 2. 路由系统
- 动态路由生成
- 路由守卫
- 面包屑导航
- 页面缓存

### 3. 状态管理
- 用户状态管理
- 应用设置管理
- 产品数据管理
- 数据持久化

### 4. HTTP 请求
- 统一请求封装
- 自动错误处理
- 请求/响应拦截
- Loading 状态管理

### 5. UI 组件
- 响应式布局
- 侧边栏导航
- 顶部导航栏
- 主题切换

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [Vue Router 文档](https://router.vuejs.org/)
- [Vite 文档](https://vitejs.dev/)

---

如有问题，请提交 Issue 或联系开发团队。
