/**
 * 文件上传API
 */
import { upload, del } from '@/utils/request'

// 定义类型
export interface UploadFile {
  url: string
  name: string
  size: number
  type: string
}

/**
 * 上传单个文件
 */
export function uploadFile(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return upload<UploadFile>('/upload', formData)
}

/**
 * 上传多个文件
 */
export function uploadFiles(files: File[]) {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  return upload<UploadFile[]>('/upload/multiple', formData)
}

/**
 * 上传图片
 */
export function uploadImage(file: File) {
  const formData = new FormData()
  formData.append('image', file)
  return upload<UploadFile>('/upload/image', formData)
}

/**
 * 删除文件
 */
export function deleteFile(url: string) {
  return del('/upload', { url })
}
