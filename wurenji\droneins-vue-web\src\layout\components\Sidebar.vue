<template>
  <div class="sidebar">
    <!-- Logo -->
    <div class="logo-container">
      <img src="/vite.svg" alt="Logo" class="logo" />
      <h1 v-if="!appStore.sidebarCollapsed" class="title">DroneIns</h1>
    </div>

    <!-- 菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      background-color="#001529"
      text-color="#fff"
      active-text-color="#1890ff"
      router
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <el-menu-item v-if="!route.children || route.children.length === 0" :index="route.path">
          <el-icon v-if="route.meta?.icon">
            <component :is="route.meta.icon" />
          </el-icon>
          <span>{{ route.meta?.title }}</span>
        </el-menu-item>

        <el-sub-menu v-else :index="route.path">
          <template #title>
            <el-icon v-if="route.meta?.icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span>{{ route.meta?.title }}</span>
          </template>
          <el-menu-item
            v-for="child in route.children"
            :key="child.path"
            :index="child.path === 'dashboard' ? '/' + child.path : route.path + '/' + child.path"
          >
            <el-icon v-if="child.meta?.icon">
              <component :is="child.meta.icon" />
            </el-icon>
            <span>{{ child.meta?.title }}</span>
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { asyncRoutes } from '@/router'

const route = useRoute()
const appStore = useAppStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 过滤菜单路由
const menuRoutes = computed(() => {
  return asyncRoutes.filter(route => !route.meta?.hidden)
})
</script>

<style scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border-bottom: 1px solid #1f1f1f;
}

.logo {
  width: 32px;
  height: 32px;
}

.title {
  margin: 0 0 0 12px;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  white-space: nowrap;
}

.el-menu {
  flex: 1;
  border: none;
}

:deep(.el-menu-item),
:deep(.el-submenu__title) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
}

:deep(.el-submenu .el-menu-item) {
  background-color: #000c17 !important;
}

:deep(.el-submenu .el-menu-item.is-active) {
  background-color: #1890ff !important;
}
</style>
