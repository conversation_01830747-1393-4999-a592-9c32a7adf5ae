{"name": "droneins-vue-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.11.0", "element-plus": "^2.11.2", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.35.0", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}