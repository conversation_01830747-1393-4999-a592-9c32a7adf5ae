<template>
  <div class="app-layout" :class="{ 'is-mobile': appStore.isMobile }">
    <!-- 侧边栏 -->
    <div
      class="sidebar-container"
      :class="{ 'is-collapsed': appStore.sidebarCollapsed }"
    >
      <AppSidebar />
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar-container">
        <AppNavbar />
      </div>

      <!-- 面包屑 -->
      <div class="breadcrumb-container" v-if="appStore.breadcrumbs.length > 0">
        <AppBreadcrumb />
      </div>

      <!-- 页面内容 -->
      <div class="content-container">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="appStore.cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="appStore.isMobile && !appStore.sidebarCollapsed"
      class="mobile-mask"
      @click="appStore.toggleSidebar"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import AppSidebar from './components/Sidebar.vue'
import AppNavbar from './components/Navbar.vue'
import AppBreadcrumb from './components/Breadcrumb.vue'

const appStore = useAppStore()

// 响应式处理
function handleResize() {
  const width = window.innerWidth
  if (width < 768) {
    appStore.setDevice('mobile')
  } else if (width < 1024) {
    appStore.setDevice('tablet')
  } else {
    appStore.setDevice('desktop')
  }
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar-container {
  width: 250px;
  background: #001529;
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar-container.is-collapsed {
  width: 64px;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar-container {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.breadcrumb-container {
  height: 40px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.content-container {
  flex: 1;
  padding: 16px;
  overflow: auto;
  background: #f5f5f5;
}

.mobile-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
}

/* 移动端适配 */
.app-layout.is-mobile .sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 999;
}

.app-layout.is-mobile .main-container {
  width: 100%;
}

/* 过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
